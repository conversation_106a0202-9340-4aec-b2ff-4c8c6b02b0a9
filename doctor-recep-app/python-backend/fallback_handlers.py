"""
CELER AI ENTERPRISE ARCHITECTURE 3.0 - PHASE 2
Fallback Handlers for Graceful Degradation

This module implements fallback mechanisms for various services
to ensure graceful degradation under failure conditions.
"""

import asyncio
import json
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass

from monitoring import logger, metrics, performance_context
from circuit_breaker import get_circuit_breaker, CircuitBreakerOpenError, CircuitBreakerTimeoutError
from message_schemas import ConsultationCreateEvent

# =====================================================
# FALLBACK CONFIGURATION
# =====================================================

FALLBACK_CONFIG = {
    'gemini_api': {
        'max_retries': 3,
        'retry_delay_seconds': 2,
        'fallback_enabled': True,
        'cache_fallback_results': True
    },
    'supabase': {
        'max_retries': 2,
        'retry_delay_seconds': 1,
        'fallback_enabled': True,
        'read_only_fallback': True
    },
    'file_processing': {
        'max_retries': 2,
        'retry_delay_seconds': 5,
        'fallback_enabled': True,
        'skip_non_critical': True
    },
    'notifications': {
        'max_retries': 1,
        'retry_delay_seconds': 10,
        'fallback_enabled': True,
        'queue_for_later': True
    }
}

# =====================================================
# FALLBACK RESULT TYPES
# =====================================================

@dataclass
class FallbackResult:
    """Result from fallback operation"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    fallback_used: bool = False
    fallback_type: Optional[str] = None
    execution_time_seconds: float = 0.0
    retry_count: int = 0

# =====================================================
# GEMINI API FALLBACK HANDLER
# =====================================================

class GeminiFallbackHandler:
    """Fallback handler for Gemini API operations"""
    
    def __init__(self):
        self.circuit_breaker = get_circuit_breaker('gemini_api', {
            'failure_threshold': 3,
            'recovery_timeout_seconds': 120,
            'timeout_seconds': 60
        })
        self.fallback_cache = {}
        
    async def generate_content_with_fallback(self, 
                                           prompt: str, 
                                           uploaded_files: List[Any],
                                           consultation_type: str,
                                           primary_func: Callable) -> FallbackResult:
        """Generate content with fallback mechanisms"""
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Try primary Gemini API call with circuit breaker
            result = await self.circuit_breaker.call(
                primary_func, prompt, uploaded_files, consultation_type
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Cache successful result for potential fallback use
            cache_key = self._generate_cache_key(prompt, consultation_type)
            self.fallback_cache[cache_key] = {
                'result': result,
                'timestamp': datetime.utcnow(),
                'consultation_type': consultation_type
            }
            
            logger.info("Gemini API call succeeded", 
                       consultation_type=consultation_type,
                       execution_time=execution_time)
            
            return FallbackResult(
                success=True,
                data=result,
                execution_time_seconds=execution_time
            )
            
        except (CircuitBreakerOpenError, CircuitBreakerTimeoutError) as e:
            logger.warning("Gemini API circuit breaker triggered", 
                          error=str(e),
                          consultation_type=consultation_type)
            
            # Try fallback mechanisms
            return await self._try_fallback_mechanisms(
                prompt, consultation_type, start_time
            )
            
        except Exception as e:
            logger.error("Gemini API call failed", 
                        error=str(e),
                        consultation_type=consultation_type)
            
            # Try fallback mechanisms
            return await self._try_fallback_mechanisms(
                prompt, consultation_type, start_time
            )
    
    async def _try_fallback_mechanisms(self, 
                                     prompt: str, 
                                     consultation_type: str,
                                     start_time: float) -> FallbackResult:
        """Try various fallback mechanisms"""
        
        # Fallback 1: Use cached similar result
        cached_result = await self._try_cached_fallback(prompt, consultation_type)
        if cached_result.success:
            execution_time = asyncio.get_event_loop().time() - start_time
            cached_result.execution_time_seconds = execution_time
            return cached_result
        
        # Fallback 2: Use template-based generation
        template_result = await self._try_template_fallback(consultation_type)
        if template_result.success:
            execution_time = asyncio.get_event_loop().time() - start_time
            template_result.execution_time_seconds = execution_time
            return template_result
        
        # Fallback 3: Minimal placeholder response
        placeholder_result = await self._try_placeholder_fallback(consultation_type)
        execution_time = asyncio.get_event_loop().time() - start_time
        placeholder_result.execution_time_seconds = execution_time
        
        return placeholder_result
    
    async def _try_cached_fallback(self, prompt: str, consultation_type: str) -> FallbackResult:
        """Try to use cached result from similar consultation"""
        try:
            # Look for cached results of same consultation type
            for cache_key, cached_data in self.fallback_cache.items():
                if (cached_data['consultation_type'] == consultation_type and
                    (datetime.utcnow() - cached_data['timestamp']).total_seconds() < 3600):  # 1 hour
                    
                    logger.info("Using cached fallback result", 
                               consultation_type=consultation_type,
                               cache_age_seconds=(datetime.utcnow() - cached_data['timestamp']).total_seconds())
                    
                    # Modify cached result to indicate it's a fallback
                    fallback_result = cached_data['result'].copy()
                    if isinstance(fallback_result, dict):
                        fallback_result['_fallback_notice'] = "This summary was generated using a cached template due to temporary service issues."
                    
                    metrics.increment_counter('gemini_fallback_used_total', 
                                            {'type': 'cached', 'consultation_type': consultation_type})
                    
                    return FallbackResult(
                        success=True,
                        data=fallback_result,
                        fallback_used=True,
                        fallback_type='cached'
                    )
            
            return FallbackResult(success=False, error="No suitable cached result found")
            
        except Exception as e:
            logger.error("Cached fallback failed", error=str(e))
            return FallbackResult(success=False, error=str(e))
    
    async def _try_template_fallback(self, consultation_type: str) -> FallbackResult:
        """Generate response using predefined templates"""
        try:
            templates = {
                'outpatient': {
                    'chief_complaint': 'Patient consultation completed',
                    'assessment': 'Clinical assessment pending - please review when service is restored',
                    'plan': 'Follow-up as clinically indicated',
                    'medications': [],
                    '_fallback_notice': 'This is a template summary generated due to temporary service issues. Please review and update as needed.'
                },
                'discharge': {
                    'discharge_diagnosis': 'Discharge summary pending',
                    'hospital_course': 'Hospital course summary pending - please review when service is restored',
                    'discharge_medications': [],
                    'follow_up': 'Follow-up as clinically indicated',
                    '_fallback_notice': 'This is a template summary generated due to temporary service issues. Please review and update as needed.'
                }
            }
            
            template = templates.get(consultation_type, templates['outpatient'])
            
            logger.info("Using template fallback", 
                       consultation_type=consultation_type)
            
            metrics.increment_counter('gemini_fallback_used_total', 
                                    {'type': 'template', 'consultation_type': consultation_type})
            
            return FallbackResult(
                success=True,
                data=template,
                fallback_used=True,
                fallback_type='template'
            )
            
        except Exception as e:
            logger.error("Template fallback failed", error=str(e))
            return FallbackResult(success=False, error=str(e))
    
    async def _try_placeholder_fallback(self, consultation_type: str) -> FallbackResult:
        """Generate minimal placeholder response"""
        try:
            placeholder = {
                'status': 'pending_review',
                'message': 'Consultation processing temporarily unavailable. Manual review required.',
                'consultation_type': consultation_type,
                'created_at': datetime.utcnow().isoformat(),
                '_fallback_notice': 'This consultation requires manual processing due to temporary service issues.'
            }
            
            logger.warning("Using placeholder fallback", 
                          consultation_type=consultation_type)
            
            metrics.increment_counter('gemini_fallback_used_total', 
                                    {'type': 'placeholder', 'consultation_type': consultation_type})
            
            return FallbackResult(
                success=True,
                data=placeholder,
                fallback_used=True,
                fallback_type='placeholder'
            )
            
        except Exception as e:
            logger.error("Placeholder fallback failed", error=str(e))
            return FallbackResult(success=False, error=str(e))
    
    def _generate_cache_key(self, prompt: str, consultation_type: str) -> str:
        """Generate cache key for prompt and consultation type"""
        import hashlib
        content = f"{consultation_type}:{prompt[:100]}"  # First 100 chars
        return hashlib.md5(content.encode()).hexdigest()

# =====================================================
# DATABASE FALLBACK HANDLER
# =====================================================

class DatabaseFallbackHandler:
    """Fallback handler for database operations"""
    
    def __init__(self):
        self.circuit_breaker = get_circuit_breaker('supabase', {
            'failure_threshold': 5,
            'recovery_timeout_seconds': 60,
            'timeout_seconds': 30
        })
        self.pending_operations = []
    
    async def execute_with_fallback(self, 
                                  operation_func: Callable,
                                  operation_type: str,
                                  *args, **kwargs) -> FallbackResult:
        """Execute database operation with fallback"""
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            result = await self.circuit_breaker.call(operation_func, *args, **kwargs)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            logger.debug("Database operation succeeded", 
                        operation_type=operation_type,
                        execution_time=execution_time)
            
            return FallbackResult(
                success=True,
                data=result,
                execution_time_seconds=execution_time
            )
            
        except (CircuitBreakerOpenError, CircuitBreakerTimeoutError) as e:
            logger.warning("Database circuit breaker triggered", 
                          operation_type=operation_type,
                          error=str(e))
            
            return await self._handle_database_fallback(
                operation_type, args, kwargs, start_time
            )
            
        except Exception as e:
            logger.error("Database operation failed", 
                        operation_type=operation_type,
                        error=str(e))
            
            return await self._handle_database_fallback(
                operation_type, args, kwargs, start_time
            )
    
    async def _handle_database_fallback(self, 
                                      operation_type: str,
                                      args: tuple,
                                      kwargs: dict,
                                      start_time: float) -> FallbackResult:
        """Handle database operation fallback"""
        
        if operation_type in ['INSERT', 'UPDATE', 'DELETE']:
            # Queue write operations for later retry
            self.pending_operations.append({
                'operation_type': operation_type,
                'args': args,
                'kwargs': kwargs,
                'timestamp': datetime.utcnow(),
                'retry_count': 0
            })
            
            logger.info("Database write operation queued for retry", 
                       operation_type=operation_type,
                       queue_size=len(self.pending_operations))
            
            metrics.increment_counter('database_fallback_used_total', 
                                    {'type': 'queued', 'operation': operation_type})
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return FallbackResult(
                success=True,  # Consider queuing as success
                data={'status': 'queued', 'message': 'Operation queued for retry'},
                fallback_used=True,
                fallback_type='queued',
                execution_time_seconds=execution_time
            )
        
        else:
            # For read operations, return empty result
            logger.warning("Database read operation failed, returning empty result", 
                          operation_type=operation_type)
            
            metrics.increment_counter('database_fallback_used_total', 
                                    {'type': 'empty', 'operation': operation_type})
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return FallbackResult(
                success=True,
                data=[],  # Empty result for reads
                fallback_used=True,
                fallback_type='empty',
                execution_time_seconds=execution_time
            )
    
    async def retry_pending_operations(self):
        """Retry pending database operations"""
        if not self.pending_operations:
            return
        
        logger.info("Retrying pending database operations", 
                   count=len(self.pending_operations))
        
        successful_retries = 0
        failed_operations = []
        
        for operation in self.pending_operations:
            try:
                # Try to execute the operation again
                # This would need to be implemented based on the specific operation
                logger.info("Retrying database operation", 
                           operation_type=operation['operation_type'])
                
                # Placeholder for actual retry logic
                successful_retries += 1
                
            except Exception as e:
                operation['retry_count'] += 1
                if operation['retry_count'] < 3:
                    failed_operations.append(operation)
                else:
                    logger.error("Database operation permanently failed", 
                               operation_type=operation['operation_type'],
                               error=str(e))
        
        # Update pending operations list
        self.pending_operations = failed_operations
        
        logger.info("Database operation retry completed", 
                   successful=successful_retries,
                   still_pending=len(self.pending_operations))

# =====================================================
# FILE PROCESSING FALLBACK HANDLER
# =====================================================

class FileProcessingFallbackHandler:
    """Fallback handler for file processing operations"""
    
    def __init__(self):
        self.circuit_breaker = get_circuit_breaker('file_processing', {
            'failure_threshold': 3,
            'recovery_timeout_seconds': 180,
            'timeout_seconds': 120
        })
    
    async def process_files_with_fallback(self, 
                                        files: List[str],
                                        processing_func: Callable) -> FallbackResult:
        """Process files with fallback mechanisms"""
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            result = await self.circuit_breaker.call(processing_func, files)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return FallbackResult(
                success=True,
                data=result,
                execution_time_seconds=execution_time
            )
            
        except (CircuitBreakerOpenError, CircuitBreakerTimeoutError) as e:
            logger.warning("File processing circuit breaker triggered", error=str(e))
            
            return await self._handle_file_processing_fallback(files, start_time)
            
        except Exception as e:
            logger.error("File processing failed", error=str(e))
            
            return await self._handle_file_processing_fallback(files, start_time)
    
    async def _handle_file_processing_fallback(self, 
                                             files: List[str],
                                             start_time: float) -> FallbackResult:
        """Handle file processing fallback"""
        
        # Try to process only critical files (e.g., primary audio)
        critical_files = files[:1] if files else []  # Just the first file
        
        if critical_files:
            try:
                logger.info("Attempting to process only critical files", 
                           critical_count=len(critical_files),
                           total_count=len(files))
                
                # Simplified processing for critical files only
                # This would be a reduced version of the processing function
                result = {
                    'processed_files': critical_files,
                    'skipped_files': files[1:],
                    'fallback_mode': True,
                    'message': 'Only critical files processed due to service limitations'
                }
                
                execution_time = asyncio.get_event_loop().time() - start_time
                
                metrics.increment_counter('file_processing_fallback_used_total', 
                                        {'type': 'critical_only'})
                
                return FallbackResult(
                    success=True,
                    data=result,
                    fallback_used=True,
                    fallback_type='critical_only',
                    execution_time_seconds=execution_time
                )
                
            except Exception as e:
                logger.error("Critical file processing fallback failed", error=str(e))
        
        # Final fallback: skip file processing entirely
        logger.warning("Skipping file processing entirely")
        
        execution_time = asyncio.get_event_loop().time() - start_time
        
        metrics.increment_counter('file_processing_fallback_used_total', 
                                {'type': 'skipped'})
        
        return FallbackResult(
            success=True,
            data={
                'processed_files': [],
                'skipped_files': files,
                'fallback_mode': True,
                'message': 'File processing skipped due to service unavailability'
            },
            fallback_used=True,
            fallback_type='skipped',
            execution_time_seconds=execution_time
        )

# =====================================================
# GLOBAL FALLBACK HANDLERS
# =====================================================

_gemini_fallback_handler: Optional[GeminiFallbackHandler] = None
_database_fallback_handler: Optional[DatabaseFallbackHandler] = None
_file_processing_fallback_handler: Optional[FileProcessingFallbackHandler] = None

def get_gemini_fallback_handler() -> GeminiFallbackHandler:
    """Get singleton Gemini fallback handler"""
    global _gemini_fallback_handler
    if _gemini_fallback_handler is None:
        _gemini_fallback_handler = GeminiFallbackHandler()
    return _gemini_fallback_handler

def get_database_fallback_handler() -> DatabaseFallbackHandler:
    """Get singleton database fallback handler"""
    global _database_fallback_handler
    if _database_fallback_handler is None:
        _database_fallback_handler = DatabaseFallbackHandler()
    return _database_fallback_handler

def get_file_processing_fallback_handler() -> FileProcessingFallbackHandler:
    """Get singleton file processing fallback handler"""
    global _file_processing_fallback_handler
    if _file_processing_fallback_handler is None:
        _file_processing_fallback_handler = FileProcessingFallbackHandler()
    return _file_processing_fallback_handler
