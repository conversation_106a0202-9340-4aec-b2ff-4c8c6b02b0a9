import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'

// Types for consultation data
export interface ConsultationData {
  id: string
  primary_audio_url: string
  additional_audio_urls?: string[]
  image_urls?: string[]
  submitted_by: string
  consultation_type: string
  patient_name?: string
  doctor_notes?: string
  additional_notes?: string
  ai_generated_note_json?: any
  edited_note_json?: any
  status: 'pending' | 'processing' | 'generated' | 'failed'
  created_at: string
  updated_at: string
}

export interface ConsultationState {
  // Current consultation being worked on
  currentConsultation: ConsultationData | null

  // Form state (moved from component props)
  patientName: string
  selectedTemplate: string
  additionalNotes: string
  images: Array<{ id: string; file: File; preview?: string }>

  // UI state (moved from component local state)
  summary: string

  // AI generation state
  isGenerating: boolean
  generationStatus: 'idle' | 'dispatched' | 'processing' | 'completed' | 'failed'
  generationError: string | null

  // Polling state
  isPolling: boolean
  pollInterval: NodeJS.Timeout | null

  // Auto-save state
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean

  // UI state
  isEditing: boolean
  selectedSection: string | null
}

export interface ConsultationActions {
  // Consultation management
  setCurrentConsultation: (consultation: ConsultationData | null) => void
  updateConsultation: (updates: Partial<ConsultationData>) => void

  // Form state management
  setPatientName: (name: string) => void
  setSelectedTemplate: (template: string) => void
  setAdditionalNotes: (notes: string) => void
  setImages: (images: Array<{ id: string; file: File; preview?: string }>) => void
  addImages: (images: Array<{ id: string; file: File; preview?: string }>) => void
  removeImage: (id: string) => void
  setSummary: (summary: string) => void

  // AI generation
  startGeneration: (consultationId: string) => void
  setGenerationStatus: (status: ConsultationState['generationStatus']) => void
  setGenerationError: (error: string | null) => void
  
  // Polling
  startPolling: (consultationId: string, interval?: number) => void
  stopPolling: () => void
  
  // Auto-save
  setSaving: (saving: boolean) => void
  setLastSaved: (date: Date) => void
  setUnsavedChanges: (hasChanges: boolean) => void
  saveEditedNote: (noteJson: any) => Promise<void>
  
  // UI state
  setEditing: (editing: boolean) => void
  setSelectedSection: (section: string | null) => void
  
  // Reset
  reset: () => void
}

type ConsultationStore = ConsultationState & ConsultationActions

const initialState: ConsultationState = {
  currentConsultation: null,
  patientName: '',
  selectedTemplate: 'outpatient',
  additionalNotes: '',
  images: [],
  summary: '',
  isGenerating: false,
  generationStatus: 'idle',
  generationError: null,
  isPolling: false,
  pollInterval: null,
  isSaving: false,
  lastSaved: null,
  hasUnsavedChanges: false,
  isEditing: false,
  selectedSection: null,
}

export const useConsultationStore = create<ConsultationStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // Consultation management
      setCurrentConsultation: (consultation) =>
        set((state) => {
          state.currentConsultation = consultation
          state.hasUnsavedChanges = false
          state.isEditing = false
          // Reset save indicators when consultation changes
          state.lastSaved = null
          state.isSaving = false

          // Populate form fields from consultation data
          if (consultation) {
            state.patientName = consultation.patient_name || ''
            state.selectedTemplate = consultation.consultation_type || 'outpatient'
            state.additionalNotes = consultation.additional_notes || ''
            state.images = [] // Clear local images when viewing existing consultation
            state.summary = '' // Clear summary for existing consultations (JsonDrivenEditor handles the data)
          } else {
            // Clear form fields for new consultation
            state.patientName = ''
            state.selectedTemplate = 'outpatient'
            state.additionalNotes = ''
            state.images = []
            state.summary = ''
          }
        }),

      updateConsultation: (updates) =>
        set((state) => {
          if (state.currentConsultation) {
            Object.assign(state.currentConsultation, updates)
          }
        }),

      // Form state management
      setPatientName: (name) =>
        set((state) => {
          state.patientName = name
          state.hasUnsavedChanges = true
        }),

      setSelectedTemplate: (template) =>
        set((state) => {
          state.selectedTemplate = template
          state.hasUnsavedChanges = true
        }),

      setAdditionalNotes: (notes) =>
        set((state) => {
          state.additionalNotes = notes
          state.hasUnsavedChanges = true
        }),

      setImages: (images) =>
        set((state) => {
          state.images = images
          state.hasUnsavedChanges = true
        }),

      addImages: (newImages) =>
        set((state) => {
          state.images = [...state.images, ...newImages]
          state.hasUnsavedChanges = true
        }),

      removeImage: (id) =>
        set((state) => {
          const image = state.images.find(img => img.id === id)
          if (image && image.preview) {
            URL.revokeObjectURL(image.preview)
          }
          state.images = state.images.filter(img => img.id !== id)
          state.hasUnsavedChanges = true
        }),

      setSummary: (summary) =>
        set((state) => {
          state.summary = summary
          state.hasUnsavedChanges = true
        }),

      // AI generation
      startGeneration: (consultationId) =>
        set((state) => {
          state.isGenerating = true
          state.generationStatus = 'dispatched'
          state.generationError = null
        }),

      setGenerationStatus: (status) =>
        set((state) => {
          state.generationStatus = status
          state.isGenerating = status === 'processing' || status === 'dispatched'
        }),

      setGenerationError: (error) =>
        set((state) => {
          state.generationError = error
          state.isGenerating = false
          state.generationStatus = 'failed'
        }),

      // Polling
      startPolling: (consultationId, interval = 3000) =>
        set((state) => {
          // Clear existing interval
          if (state.pollInterval) {
            clearInterval(state.pollInterval)
          }

          state.isPolling = true
          state.pollInterval = setInterval(async () => {
            try {
              const response = await fetch(`/api/job-status/${consultationId}`)
              
              if (response.status === 503) {
                // Service unavailable - Redis is down, keep polling
                console.log('⚠️ Status service temporarily unavailable, continuing to poll...')
                return
              }
              
              if (response.status === 202) {
                // Still processing
                get().setGenerationStatus('processing')
                return
              }
              
              if (response.ok) {
                const data = await response.json()
                
                if (data.status === 'generated') {
                  // Success! Update consultation and stop polling
                  get().updateConsultation({
                    ai_generated_note_json: data.data,
                    status: 'generated',
                    updated_at: data.updatedAt
                  })
                  get().setGenerationStatus('completed')
                  get().stopPolling()
                } else if (data.status === 'failed') {
                  // Failed
                  get().setGenerationError(data.error || 'Generation failed')
                  get().stopPolling()
                }
              }
            } catch (error) {
              console.error('Polling error:', error)
              // Don't stop polling on network errors - keep trying
            }
          }, interval)
        }),

      stopPolling: () =>
        set((state) => {
          if (state.pollInterval) {
            clearInterval(state.pollInterval)
            state.pollInterval = null
          }
          state.isPolling = false
        }),

      // Auto-save
      setSaving: (saving) =>
        set((state) => {
          state.isSaving = saving
        }),

      setLastSaved: (date) =>
        set((state) => {
          state.lastSaved = date
          state.hasUnsavedChanges = false
        }),

      setUnsavedChanges: (hasChanges) =>
        set((state) => {
          state.hasUnsavedChanges = hasChanges
        }),

      saveEditedNote: async (noteJson) => {
        const state = get()

        if (!state.currentConsultation) {
          return
        }

        set((state) => {
          state.isSaving = true
        })

        try {
          const response = await fetch(`/api/consultations/${state.currentConsultation.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              edited_note_json: noteJson
            }),
          })

          if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`Failed to save: ${response.status} ${errorText}`)
          }

          const result = await response.json()

          set((state) => {
            if (state.currentConsultation) {
              state.currentConsultation.edited_note_json = noteJson
              state.currentConsultation.updated_at = new Date().toISOString()
            }
            state.lastSaved = new Date()
            state.hasUnsavedChanges = false
            state.isSaving = false
          })

          // Return the updated consultation data so parent can refresh
          return result.data
        } catch (error) {
          console.error('Save error:', error)
          set((state) => {
            state.isSaving = false
          })
          throw error
        }
      },

      // UI state
      setEditing: (editing) =>
        set((state) => {
          state.isEditing = editing
        }),

      setSelectedSection: (section) =>
        set((state) => {
          state.selectedSection = section
        }),

      // Reset
      reset: () =>
        set((state) => {
          // Clear polling interval
          if (state.pollInterval) {
            clearInterval(state.pollInterval)
          }
          
          // Reset to initial state
          Object.assign(state, initialState)
        }),
    }))
  )
)
