/**
 * CELER AI ENTERPRISE ARCHITECTURE 3.0
 * Event Bus - Google Pub/Sub Client with Reliability Features
 * 
 * This module provides reliable event publishing with retry policies,
 * dead letter queues, and message ordering guarantees.
 */

import { PubSub, Topic, Message } from '@google-cloud/pubsub'
import { ConsultationEvent } from '@/lib/validation/consultation-schemas'
import { getRedisClient } from '@/lib/redis'

// =====================================================
// CONFIGURATION
// =====================================================

const PUBSUB_CONFIG = {
  // Topic configuration
  topics: {
    consultation_create: 'consultation-create',
    consultation_update: 'consultation-update',
    consultation_delete: 'consultation-delete'
  },
  
  // Dead letter queues
  deadLetterQueues: {
    consultation_create: 'consultation-create-dlq',
    consultation_update: 'consultation-update-dlq',
    consultation_delete: 'consultation-delete-dlq'
  },
  
  // Retry configuration
  retry: {
    initialDelayMs: 1000,
    maxDelayMs: 60000,
    multiplier: 2.0,
    maxAttempts: 5
  },
  
  // Message configuration
  message: {
    maxSizeBytes: 10 * 1024 * 1024, // 10MB
    orderingKeyEnabled: true,
    enableMessageOrdering: true
  }
}

// =====================================================
// TYPES AND INTERFACES
// =====================================================

export interface PublishOptions {
  orderingKey?: string
  attributes?: Record<string, string>
  retryPolicy?: {
    maxAttempts?: number
    initialDelayMs?: number
    maxDelayMs?: number
    multiplier?: number
  }
  deadLetterQueue?: string
  priority?: 'low' | 'normal' | 'high'
}

export interface PublishResult {
  success: boolean
  messageId?: string
  error?: string
  correlationId: string
  publishedAt: Date
  retryCount: number
}

export interface EventBusMetrics {
  messagesPublished: number
  messagesDelivered: number
  messagesFailed: number
  averageLatencyMs: number
  deadLetterCount: number
}

// =====================================================
// EVENT BUS CLIENT CLASS
// =====================================================

export class EventBusClient {
  private pubsub: PubSub | null = null
  private topics: Map<string, Topic> = new Map()
  private redis = getRedisClient()
  private metrics: EventBusMetrics = {
    messagesPublished: 0,
    messagesDelivered: 0,
    messagesFailed: 0,
    averageLatencyMs: 0,
    deadLetterCount: 0
  }

  constructor() {
    // Lazy initialization to avoid build-time execution
    console.log('🚀 Event Bus Client created (lazy initialization)')
  }

  /**
   * Initialize PubSub client lazily
   */
  private initializePubSub(): PubSub {
    if (!this.pubsub) {
      this.pubsub = new PubSub({
        projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
        keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE,
      })
      console.log('🚀 PubSub client initialized')
    }
    return this.pubsub
  }

  /**
   * Publish consultation creation event
   */
  async publishConsultationCreate(
    event: ConsultationEvent,
    options: PublishOptions = {}
  ): Promise<PublishResult> {
    return this.publishEvent('consultation_create', event, {
      ...options,
      orderingKey: options.orderingKey || event.consultation_id,
      attributes: {
        ...options.attributes,
        event_type: 'consultation.create',
        correlation_id: event.correlation_id,
        user_id: event.user_id,
        consultation_type: event.consultation_type
      }
    })
  }

  /**
   * Generic event publishing with reliability features
   */
  async publishEvent(
    topicName: keyof typeof PUBSUB_CONFIG.topics,
    eventData: any,
    options: PublishOptions = {}
  ): Promise<PublishResult> {
    const startTime = Date.now()
    const correlationId = eventData.correlation_id || crypto.randomUUID()
    
    try {
      console.log(`📤 Publishing event to ${topicName} with correlation ID: ${correlationId}`)
      
      // Get or create topic
      const topic = await this.getOrCreateTopic(topicName)
      
      // Prepare message
      const messageData = {
        ...eventData,
        published_at: new Date().toISOString(),
        schema_version: eventData.schema_version || '1.0'
      }
      
      // Validate message size
      const messageSize = Buffer.byteLength(JSON.stringify(messageData))
      if (messageSize > PUBSUB_CONFIG.message.maxSizeBytes) {
        throw new Error(`Message size ${messageSize} exceeds limit ${PUBSUB_CONFIG.message.maxSizeBytes}`)
      }
      
      // Set up message attributes
      const attributes = {
        correlation_id: correlationId,
        event_type: eventData.event_type || topicName,
        schema_version: eventData.schema_version || '1.0',
        priority: options.priority || 'normal',
        retry_count: '0',
        ...options.attributes
      }
      
      // Message ordering is configured at the topic level during creation
      // The orderingKey will be used in the publish call below
      
      // Publish message with retry logic
      const messageId = await this.publishWithRetry(
        topic,
        messageData,
        attributes,
        options
      )
      
      // Update metrics
      this.metrics.messagesPublished++
      const latency = Date.now() - startTime
      this.updateAverageLatency(latency)
      
      // Store publish record for tracking
      await this.storePublishRecord(correlationId, topicName, messageId, messageData)
      
      console.log(`✅ Event published successfully: ${messageId}`)
      
      return {
        success: true,
        messageId,
        correlationId,
        publishedAt: new Date(),
        retryCount: 0
      }
      
    } catch (error) {
      console.error(`❌ Failed to publish event to ${topicName}:`, error)
      
      this.metrics.messagesFailed++
      
      // Store failure record
      await this.storeFailureRecord(correlationId, topicName, error, eventData)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        correlationId,
        publishedAt: new Date(),
        retryCount: 0
      }
    }
  }

  /**
   * Publish message with retry logic
   */
  private async publishWithRetry(
    topic: Topic,
    messageData: any,
    attributes: Record<string, string>,
    options: PublishOptions
  ): Promise<string> {
    const retryPolicy = {
      ...PUBSUB_CONFIG.retry,
      ...options.retryPolicy
    }
    
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= retryPolicy.maxAttempts; attempt++) {
      try {
        // Calculate delay for this attempt
        if (attempt > 1) {
          const delay = Math.min(
            retryPolicy.initialDelayMs * Math.pow(retryPolicy.multiplier, attempt - 2),
            retryPolicy.maxDelayMs
          )
          
          console.log(`⏳ Retrying publish attempt ${attempt}/${retryPolicy.maxAttempts} after ${delay}ms`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
        
        // Update retry count in attributes
        attributes.retry_count = (attempt - 1).toString()
        
        // Publish message
        const messageId = await topic.publishMessage({
          data: Buffer.from(JSON.stringify(messageData)),
          attributes,
          orderingKey: options.orderingKey
        })
        
        console.log(`✅ Message published on attempt ${attempt}: ${messageId}`)
        return messageId
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        console.error(`❌ Publish attempt ${attempt} failed:`, error)
        
        // If this is the last attempt, check for dead letter queue
        if (attempt === retryPolicy.maxAttempts && options.deadLetterQueue) {
          await this.sendToDeadLetterQueue(options.deadLetterQueue, messageData, attributes, lastError)
        }
      }
    }
    
    throw lastError || new Error('All retry attempts failed')
  }

  /**
   * Send message to dead letter queue
   */
  private async sendToDeadLetterQueue(
    dlqName: string,
    messageData: any,
    attributes: Record<string, string>,
    originalError: Error
  ): Promise<void> {
    try {
      console.log(`💀 Sending message to dead letter queue: ${dlqName}`)
      
      const dlqTopic = this.pubsub.topic(dlqName)
      
      const dlqMessage = {
        ...messageData,
        dead_letter_reason: originalError.message,
        dead_letter_timestamp: new Date().toISOString(),
        original_attributes: attributes
      }
      
      await dlqTopic.publishMessage({
        data: Buffer.from(JSON.stringify(dlqMessage)),
        attributes: {
          ...attributes,
          dead_letter: 'true',
          original_error: originalError.message
        }
      })
      
      this.metrics.deadLetterCount++
      console.log(`✅ Message sent to dead letter queue: ${dlqName}`)
      
    } catch (error) {
      console.error(`❌ Failed to send message to dead letter queue:`, error)
    }
  }

  /**
   * Get or create topic
   */
  private async getOrCreateTopic(topicName: keyof typeof PUBSUB_CONFIG.topics): Promise<Topic> {
    const fullTopicName = PUBSUB_CONFIG.topics[topicName]
    
    if (this.topics.has(fullTopicName)) {
      return this.topics.get(fullTopicName)!
    }
    
    try {
      const topic = this.pubsub.topic(fullTopicName)
      
      // Check if topic exists, create if not
      const [exists] = await topic.exists()
      if (!exists) {
        console.log(`📝 Creating topic: ${fullTopicName}`)
        await topic.create()
      }
      
      this.topics.set(fullTopicName, topic)
      return topic
      
    } catch (error) {
      console.error(`❌ Failed to get/create topic ${fullTopicName}:`, error)
      throw error
    }
  }

  /**
   * Store publish record for tracking
   */
  private async storePublishRecord(
    correlationId: string,
    topicName: string,
    messageId: string,
    messageData: any
  ): Promise<void> {
    try {
      const recordKey = `event:published:${correlationId}`
      const record = {
        correlation_id: correlationId,
        topic_name: topicName,
        message_id: messageId,
        published_at: new Date().toISOString(),
        event_type: messageData.event_type,
        status: 'published'
      }
      
      await this.redis.hSet(recordKey, record)
      await this.redis.expire(recordKey, 86400) // 24 hours TTL
      
    } catch (error) {
      console.error('Failed to store publish record:', error)
      // Don't throw - this is for tracking only
    }
  }

  /**
   * Store failure record
   */
  private async storeFailureRecord(
    correlationId: string,
    topicName: string,
    error: any,
    messageData: any
  ): Promise<void> {
    try {
      const recordKey = `event:failed:${correlationId}`
      const record = {
        correlation_id: correlationId,
        topic_name: topicName,
        error_message: error instanceof Error ? error.message : 'Unknown error',
        failed_at: new Date().toISOString(),
        event_type: messageData.event_type,
        status: 'failed'
      }
      
      await this.redis.hSet(recordKey, record)
      await this.redis.expire(recordKey, 86400) // 24 hours TTL
      
    } catch (error) {
      console.error('Failed to store failure record:', error)
      // Don't throw - this is for tracking only
    }
  }

  /**
   * Update average latency metric
   */
  private updateAverageLatency(latency: number): void {
    const totalMessages = this.metrics.messagesPublished
    this.metrics.averageLatencyMs = 
      ((this.metrics.averageLatencyMs * (totalMessages - 1)) + latency) / totalMessages
  }

  /**
   * Get event bus metrics
   */
  getMetrics(): EventBusMetrics {
    return { ...this.metrics }
  }

  /**
   * Health check for event bus
   */
  async healthCheck(): Promise<{
    healthy: boolean
    topics: Record<string, boolean>
    error?: string
  }> {
    try {
      const topicHealth: Record<string, boolean> = {}
      
      // Check each topic
      for (const [key, topicName] of Object.entries(PUBSUB_CONFIG.topics)) {
        try {
          const topic = this.pubsub.topic(topicName)
          const [exists] = await topic.exists()
          topicHealth[key] = exists
        } catch (error) {
          topicHealth[key] = false
        }
      }
      
      const allHealthy = Object.values(topicHealth).every(healthy => healthy)
      
      return {
        healthy: allHealthy,
        topics: topicHealth
      }
      
    } catch (error) {
      return {
        healthy: false,
        topics: {},
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// =====================================================
// SINGLETON INSTANCE
// =====================================================

let eventBusClient: EventBusClient | null = null

export function getEventBusClient(): EventBusClient {
  if (!eventBusClient) {
    eventBusClient = new EventBusClient()
  }
  return eventBusClient
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Publish consultation creation event (convenience function)
 */
export async function publishConsultationCreate(
  event: ConsultationEvent,
  options?: PublishOptions
): Promise<PublishResult> {
  const client = getEventBusClient()
  return client.publishConsultationCreate(event, options)
}

/**
 * Get event bus health status
 */
export async function getEventBusHealth() {
  const client = getEventBusClient()
  return client.healthCheck()
}

/**
 * Get event bus metrics
 */
export function getEventBusMetrics(): EventBusMetrics {
  const client = getEventBusClient()
  return client.getMetrics()
}
